{"name": "routeclouds-backend", "version": "1.0.0", "description": "RouteClouds E-Commerce Backend API", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts", "build": "tsc", "migrate": "node -e \"console.log('Running database migration...'); require('./dist/database/migrate.js');\"", "seed": "node -e \"console.log('Running database seeding...'); require('./dist/database/seed.js');\""}, "dependencies": {"bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "morgan": "^1.10.0", "pg": "^8.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.2", "@types/cors": "^2.8.13", "@types/express": "^4.17.17", "@types/jsonwebtoken": "^9.0.2", "@types/morgan": "^1.9.4", "@types/node": "^20.19.7", "@types/pg": "^8.10.0", "ts-node-dev": "^2.0.0", "typescript": "^5.8.3"}}